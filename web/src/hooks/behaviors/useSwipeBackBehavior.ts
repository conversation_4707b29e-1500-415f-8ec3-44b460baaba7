import * as React from 'react';
import { useThrottle } from 'react-use';
import { TouchOrMouseEventType } from '../../utilities/event';

export type TouchInfoType = {
  xStart: number;
  yStart: number;
  xCurrent: number;
  yCurrent: number;
  // false 記録しない, true 記録中, 1 方向判定済, 2 対象の軸と異なる
  isCapturing: boolean | 1 | 2;
}

type UseSwipeBackReturnType = [
  touchLength: number,
  onTouchStart: (e: TouchOrMouseEventType) => void,
  onTouchMove: (e: TouchOrMouseEventType) => void,
  onTouchEnd: (e: TouchOrMouseEventType) => void,
  touchInfoRef: React.MutableRefObject<TouchInfoType>,
];

/**
 * スワイプした距離(%)を計算する
 * @param touchInfo
 * @param swipeDir
 */
export function calculateTouchLength(touchInfo: TouchInfoType, swipeDir: 'x' | 'y'): number {
  // 横方向判定済のときにだけ移動させる
  if (touchInfo.isCapturing !== 1) {
    return 0;
  }

  // タッチ開始時点からの差分
  const diff = swipeDir === 'x'
    ? Math.round(touchInfo.xCurrent - touchInfo.xStart)
    : Math.round(touchInfo.yCurrent - touchInfo.yStart);

  // window幅に対するパーセンテージ
  const percent = Math.round((diff / window.innerWidth) * 100);
  // このパーセンテージ以上動かしたときにだけ反応させる
  const percentOffset = 8;
  // 0以上の値にする
  return Math.max(percent - percentOffset, 0);
}

/**
 * 1以上のtouchLengthを更新する
 * @param touchLength
 * @param setTouchLength
 */
export function updateTouchLength(touchLength: number, setTouchLength: (tl: number) => void): void {
  if (touchLength > 0) setTouchLength(touchLength);
}

/**
 * 初期状態のTouchInfoを返却する
 */
export function createInitialTouchInfo(): TouchInfoType {
  return {
    xStart: 0, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: false,
  };
}

/**
 * 選択範囲が存在するかどうかを確認する
 * @returns trueのときは選択範囲が存在する
 */
export function isRangeSelected(): boolean {
  const selection = window.getSelection();
  if (!selection) return false;
  return selection.type === 'Range';
}

/**
 * タッチ開始時のデータを作成
 * @param e
 */
export function createTouchInfoOnStart(e: TouchOrMouseEventType): TouchInfoType {
  // テキスト選択中はキャンセル
  if (isRangeSelected()) {
    return createInitialTouchInfo();
  }

  if (e.nativeEvent instanceof TouchEvent) {
    const touch = e.nativeEvent.touches[0];
    return {
      xStart: touch.clientX ?? 0,
      yStart: touch.clientY ?? 0,
      xCurrent: 0,
      yCurrent: 0,
      isCapturing: true,
    };
  }

  return {
    xStart: e.nativeEvent.clientX ?? 0,
    yStart: e.nativeEvent.clientY ?? 0,
    xCurrent: 0,
    yCurrent: 0,
    isCapturing: true,
  };
}

/**
 * touchInfoの値からXYのスワイプ方向を確定させる
 * @param touchInfo
 * @param swipeDir
 */
export function detectAxis(touchInfo: TouchInfoType, swipeDir: 'x' | 'y' = 'x'): TouchInfoType {
  if (touchInfo.isCapturing !== true) {
    // falseや方向が確定している場合はskip
    return touchInfo;
  }

  // x軸差分
  const diffX = swipeDir === 'x'
    // x方向スワイプ判定時
    // xCurrent - xStart の差 (右スワイプの判定基準)
    ? Math.round(touchInfo.xCurrent - touchInfo.xStart)
    // y方向スワイプ判定時
    // xCurrent - xStart の絶対値 (左右スワイプ両方向の判定基準)
    : Math.abs(Math.round(touchInfo.xCurrent - touchInfo.xStart));

  // y軸差分
  const diffY = swipeDir === 'y'
    // x方向スワイプ判定時
    // yCurrent - yStart の絶対値 (上下スワイプ両方向の判定基準)
    ? Math.abs(Math.round(touchInfo.yCurrent - touchInfo.yStart))
    // y方向スワイプ判定時
    // yCurrent - yStart の差 (下スワイプの判定基準)
    : Math.round(touchInfo.yCurrent - touchInfo.yStart);

  // しきい値
  const DIFF_X_THRESHOLD = 25;
  const DIFF_Y_THRESHOLD = 25;

  if (swipeDir === 'x') {
    if (diffY >= DIFF_Y_THRESHOLD) {
      return {
        ...touchInfo,
        isCapturing: 2,
      };
    }

    if (diffX >= DIFF_X_THRESHOLD) {
      return {
        ...touchInfo,
        isCapturing: 1,
      };
    }

    return touchInfo;
  }

  // swipeDir === 'y'のケース
  if (diffX >= DIFF_X_THRESHOLD) {
    return {
      ...touchInfo,
      isCapturing: 2,
    };
  }

  if (diffY >= DIFF_Y_THRESHOLD) {
    return {
      ...touchInfo,
      isCapturing: 1,
    };
  }

  return touchInfo;

}

/**
 * タッチ移動中のデータを生成
 * @param e
 * @param touchInfo
 * @param touchLength
 * @param setTouchLength
 * @param swipeDir
 */
export function createTouchInfoOnMove(
  e: TouchOrMouseEventType,
  touchInfo: TouchInfoType,
  touchLength: number,
  setTouchLength: (n: number) => void,
  swipeDir: 'x' | 'y' = 'x',
): TouchInfoType {

  // テキスト選択中はキャンセル
  if (isRangeSelected()) {
    setTouchLength(0);
    return createInitialTouchInfo();
  }

  if (!touchInfo.isCapturing) return touchInfo;

  const result = detectAxis({
    ...touchInfo,
    xCurrent: e.nativeEvent instanceof TouchEvent
      ? e.nativeEvent.touches[0].clientX
      : e.nativeEvent.clientX,
    yCurrent: e.nativeEvent instanceof TouchEvent
      ? e.nativeEvent.touches[0].clientY
      : e.nativeEvent.clientY,
  }, swipeDir);

  updateTouchLength(calculateTouchLength(result, swipeDir), setTouchLength);
  return result;
}

/**
 * touchLengthがthresholdを越えたときにonSwipeBackを実行してタッチ終了データを生成
 * @param touchLength
 * @param threshold
 * @param setTouchLength
 * @param onSwipeBack
 */
export function resetTouchLengthImpl(
  touchLength: number,
  threshold: number,
  setTouchLength: (n: number) => void,
  onSwipeBack?: () => void,
): TouchInfoType {

  if (touchLength > threshold && onSwipeBack) onSwipeBack();
  setTouchLength(0);
  return createInitialTouchInfo();
}

/**
 * スワイプでコールバックを発火する機能
 * @param thresholdToSwipeBack
 * @param onSwipeBack
 * @param direction スワイプの方向を指定する。デフォルトではy（上下）
 */
const useSwipeBackBehavior = (
  thresholdToSwipeBack: number, onSwipeBack: (() => void) | undefined,
  direction: 'x' | 'y' = 'y',
): UseSwipeBackReturnType => {

  const touchInfo = React.useRef<TouchInfoType>(createInitialTouchInfo());
  const [touchLength, setTouchLength] = React.useState(0);
  const throttledTouchLength = useThrottle(touchLength, 10);
  const [swipeDir] = React.useState(direction);

  const onTouchStart = React.useCallback((e: TouchOrMouseEventType) => {
    touchInfo.current = createTouchInfoOnStart(e);
  }, []);

  const onTouchMove = React.useCallback((e: TouchOrMouseEventType) => {
    touchInfo.current = createTouchInfoOnMove(
      e, touchInfo.current, touchLength, setTouchLength, swipeDir,
    );
  }, [swipeDir, touchLength]);

  const onTouchEnd = React.useCallback(() => {
    touchInfo.current = resetTouchLengthImpl(
      touchLength, thresholdToSwipeBack, setTouchLength, onSwipeBack,
    );
  }, [onSwipeBack, thresholdToSwipeBack, touchLength]);

  return [throttledTouchLength, onTouchStart, onTouchMove, onTouchEnd, touchInfo];
};

export default useSwipeBackBehavior;
