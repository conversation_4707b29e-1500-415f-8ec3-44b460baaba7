/* eslint-disable @typescript-eslint/no-unused-vars */
import { Client } from '@microsoft/microsoft-graph-client';
import { createGraphClient } from '@avanade-teams/auth';
import React from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { IGraphUser, GraphGroupIdsResponse } from '../../types/IGraphUser';
import environment from '../../utilities/environment';
import wait from '../../utilities/wait';
// eslint-disable-next-line import/no-cycle
import { RequestQueueItem } from '../../types/RequestQueueItem';

export type FetchUser = (oid?: string) => Promise<IGraphUser>
export type FetchUsers = (oids: string[]) => Promise<IGraphUser[]>;
export type FetchGroupIds = () => Promise<string[]>;

type TokenProvider = (() => Promise<string>) | undefined;

export const UseGraphApiError = {
  TOKEN_PROVIDER_NOT_AVAILABLE: 'tokenProvider is not provided yet',
  REQUIRED_PARAM_NOT_AVAILABLE: 'required parameters are not provided',
};

const RECOVERABLE_STATUSES = [
  // Conflict
  409,
  // Precondition Failed
  412,
  // Too Many Requests
  429,
  // Unavailable
  503,
  // Gateway Timeout
  504,
];

/**
 * Graph APIのクライアントを構成する
 * @param tokenProvider
 * @param baseUrl
 * @param customHost
 */
export function initGraphClient(
  tokenProvider: TokenProvider,
): Client | undefined {
  return createGraphClient(
    tokenProvider,
    {
      defaultVersion: 'v1.0',
      fetchOptions: {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    },
  );
}

/**
 * @returns reject時にはErrorまたはGraphErrorを返却する
 */
export async function fetchUserImpl(
  tokenProvider: WeakTokenProvider,
  oid?: string,
): Promise<IGraphUser> {

  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  const path = oid ? `users/${oid}` : 'me';
  const res = await client
    .api(path)
    .get();
  return res;
}

export function isErrorResponse(response: IBatchResponse): response is IErrorResponse {
  return response.status !== 200 && !RECOVERABLE_STATUSES.includes(response.status);
}

export function isRecoverableResponse(response: IBatchResponse): response is IRecoverableResponse {
  return RECOVERABLE_STATUSES.includes(response.status);
}

export function isSuccessResponse<T>(response: IBatchResponse): response is ISuccessResponse<T> {
  return response.status === 200;
}

export interface IBatchResponses<T> {
  responses: T[];
  recoverable: IRecoverableResponse[];
  errors: IErrorResponse[];
}

export interface IBatchResponseStatus<T> extends IBatchResponses<T> {
  totalTooManyRequests: number;
  tooManyRequests: { maxRetryAfter: number, count: number }[]
}

export type ISerialResponseStatus<T> = IBatchResponseStatus<T>;

export interface IRequestPayload {
  url: string;
  method: 'GET' | 'POST';
  id: string;
}

export interface IBatchResponse {
  body: unknown;
  id: string;
  status: number;
  headers: { [key: string]: string };
}

interface IErrorResponse extends IBatchResponse {
  body: IErrorResponseBody;
}

interface IRecoverableResponse extends IBatchResponse {
  status: number;
  body: IErrorResponseBody;
}
export interface IErrorResponseBody {
  code: string;
  message: string;
}

export interface ISuccessResponse<T> extends IBatchResponse {
  body: T;
}

export type BulkResponseConverterType<T, U = T> =
  (item: ISuccessResponse<T>) => U;

function filterErrorOrSuccess<T, U>(
  { responses, errors, recoverable }: {
    responses: U[], errors: IErrorResponse[], recoverable: IRecoverableResponse[]
  },
  response: IBatchResponse,
  converter: BulkResponseConverterType<T, U>,
) {
  if (isErrorResponse(response)) errors.push(response);
  if (isRecoverableResponse(response)) recoverable.push(response);
  if (isSuccessResponse<T>(response)) responses.push(converter(response));

  return { responses, errors, recoverable };
}

async function sendBatchRequestsOneTry<T, U>(
  client: Client,
  allRequests: IRequestPayload[],
  converter: BulkResponseConverterType<T, U>,
): Promise<IBatchResponses<U>> {
  const req = [...allRequests];
  const errors: IErrorResponse[] = [];
  const responses: U[] = [];
  const recoverable: IRecoverableResponse[] = [];
  while (req.length > 0) {
    const requests = req.splice(0, environment.REACT_APP_BATCH_REQUEST_CHUNK_SIZE);
    const batch = client.api('$batch').post({ requests });
    const result = batch
      .then((res: { responses: (IBatchResponse)[] }) => res.responses.reduce(
        (results, response) => filterErrorOrSuccess(results, response, converter),
        { responses: [], errors: [], recoverable: [] } as IBatchResponses<U>,
      ));

    // 並列呼び出しの調整のために直列呼び出しにしている
    // eslint-disable-next-line no-await-in-loop
    await result.then(({ errors: err, responses: res, recoverable: rec }) => {
      errors.push(...err);
      responses.push(...res);
      recoverable.push(...rec);
    });
  }
  return { errors, responses, recoverable };
}

function getMaxWaitSeconds(recoverable: IRecoverableResponse[]): number {
  const retryAfters = recoverable.map(({ headers }) => Number.parseInt(headers['Retry-After'], 10)).filter((n) => n > 0);
  return Math.max(0, ...retryAfters);
}

function getAverageWaitAfterSeconds(recoverable: IRecoverableResponse[]): number {
  const retryAfters = recoverable.map(({ headers }) => Number.parseInt(headers['Retry-After'], 10)).filter((n) => n > 0);
  if (retryAfters.length > 0) {
    return retryAfters.reduce((sum, n) => sum + n) / retryAfters.length;
  }
  return 0;
}

function waitForRecovery(waitSeconds: number, additionalWaitingMsec: number): Promise<void> {
  // 1件もRetry-Afterがなかったときは規定時間を指定
  const waitMsec = waitSeconds * 1000 || environment.REACT_APP_RETRY_DEAFULT_TIME;
  return wait(waitMsec + additionalWaitingMsec);
}

async function sendBulkRequests<T, U>(
  client: Client,
  allRequests: IRequestPayload[],
  converter: BulkResponseConverterType<T, U>,
  cancellationRef: React.MutableRefObject<boolean>,
  appendSearchResults?: (items: U[]) => void,
): Promise<IBatchResponseStatus<U>> {
  let requests = [...allRequests];
  const allErrors = [] as IErrorResponse[];
  const results = [] as U[];
  let remainingRecoverable = [] as IRecoverableResponse[];
  const tooManyRequests = [] as {
    maxRetryAfter: number, averageRetryAfter: number, count: number
  }[];
  let totalTooManyRequests = 0;
  for (
    let i = 0;
    environment.REACT_APP_RETRY_COUNTS === -1 || i < environment.REACT_APP_RETRY_COUNTS;
    i += 1
  ) {
    if (cancellationRef.current) break;
    const {
      responses, errors, recoverable,
      // 直列でしないといけない処理なので
      // eslint-disable-next-line no-await-in-loop
    } = await sendBatchRequestsOneTry(client, requests, converter);

    results.push(...responses);
    appendSearchResults?.(responses);
    allErrors.push(...errors);
    remainingRecoverable = [...recoverable];
    if (recoverable.length === 0) {
      break;
    }
    requests = recoverable.map(
      (error) => allRequests.find((request) => error.id === request.id),
    ).filter((item) => item !== undefined) as IRequestPayload[];

    const additionalWaitingMsec = i * environment.REACT_APP_ADDITIONAL_WAITING_MSEC;
    const maxWaitSec = getMaxWaitSeconds(recoverable);
    const aveWaitSec = getAverageWaitAfterSeconds(recoverable);
    const count = recoverable.filter((res) => res.status === 429).length;
    totalTooManyRequests += count;
    tooManyRequests.push({
      maxRetryAfter: maxWaitSec,
      averageRetryAfter: aveWaitSec,
      count,
    });
    // 規定秒数待つ必要があるので
    // eslint-disable-next-line no-await-in-loop
    await waitForRecovery(maxWaitSec, additionalWaitingMsec);
  }
  return {
    responses: results,
    errors: allErrors,
    recoverable: cancellationRef.current ? [] : remainingRecoverable,
    tooManyRequests,
    totalTooManyRequests,
  };
}

export function sendRequests<T, U = T>(
  tokenProvider: WeakTokenProvider,
  allRequests: IRequestPayload[],
  cancellationRef: React.MutableRefObject<boolean>,
  appendSearchResults?: (items: U[]) => void,
  converter: BulkResponseConverterType<T, U> = (({ body }) => body as unknown as U),
): Promise<IBatchResponseStatus<U>> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  return sendBulkRequests<T, U>(client,
    allRequests,
    converter,
    cancellationRef,
    appendSearchResults);
}
