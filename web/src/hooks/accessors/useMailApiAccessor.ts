import React from 'react';
import { IGraphUser } from '../../types/IGraphUser';
import { IMailAttachmentResponse, IMailResponse } from '../../types/IMailResponse';
import { WeakTokenProvider } from '../../types/TokenProvider';
import {
  FetchUser,
  IBatchResponseStatus,
  IErrorResponseBody,
  UseGraphApiError,
  initGraphClient,
  sendRequests,
} from './useGraphApiAccessor';

export interface IMailAttachmentParams {
  id: string,
  mailId: string,
  contentId: string,
}

export type UseMailApiReturnType = {
  fetchUser: FetchUser | undefined,
  fetchMailDetail: FetchMailDetail | undefined,
  fetchMailDetailAttachments:FetchMailDetailAttachments| undefined,
  fetchMailAttachment: FetchMailAttachment | undefined,
  fetchMailAttachments: FetchMailAttachments | undefined,
}
export type FetchMailDetail = (mailId: string) => Promise<IMailResponse>;
export type FetchMailDetailAttachments = (
mailId: string, attachmentId:string) => Promise<IMailAttachmentResponse>;
export type FetchMailAttachment = (params: IMailAttachmentParams) => Promise<Blob>;
export type FetchMailAttachments = (
  paramsList: IMailAttachmentParams[],
) => Promise<({ id: string, response: (Blob | IErrorResponseBody) })[]>
export type FetchMails = (
  mailIds: string[],
  appendSearchResults: (items: IMailResponse[]) => void
) => Promise<IBatchResponseStatus<IMailResponse>>

/**
 * @returns reject時にはErrorまたはGraphErrorを返却する
 */
export async function fetchUserImpl(
  tokenProvider: WeakTokenProvider,
  oid?: string,
): Promise<IGraphUser> {

  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  const path = oid ? `users/${oid}` : 'me';
  const res = await client
    .api(path)
    .get();
  return res;
}

/**
 * @returns reject時にはErrorまたはGraphErrorを返却する
 */
async function fetchMailAttachmentImpl(
  tokenProvider: WeakTokenProvider, params: IMailAttachmentParams,
): Promise<Blob> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  // メール本文内の添付ファイルを取得する
  const res = await client
    .api(`me/messages/${params.mailId}/attachments/${params.id}/$value`)
    .get();
  return res;
}

/**
 * @returns reject時にはErrorまたはGraphErrorを返却する
 */
async function fetchMailDetailImpl(
  tokenProvider: WeakTokenProvider, mailId: string,
): Promise<IMailResponse> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  const res = await client
    // 添付ファイル情報を含むメール詳細を取得
    .api(`me/messages/${mailId}/?$expand=attachments($select=name,contentType,size,isInline,id,name,microsoft.graph.fileAttachment/contentId)`)
    .get();
  return res;
}

/**
 * @returns reject時にはErrorまたはGraphErrorを返却する
 * 添付ファイルのダウンロード用に取得
 */
async function fetchMailDetailAttachmentsImpl(
  tokenProvider: WeakTokenProvider, mailId: string, attachmentId:string,
): Promise<IMailAttachmentResponse> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  // 添付ファイルIDを元にファイルを取得
  const res = await client
    .api(`me/messages/${mailId}/attachments/${attachmentId}`)
    .get();
  return res;
}

export function fetchBulkAttachments(
  tokenProvider: WeakTokenProvider,
  paramsList: IMailAttachmentParams[],
  cancellationRef: React.MutableRefObject<boolean>,
) {
  return sendRequests<Blob, { id: string, response: Blob }>(
    tokenProvider, paramsList.map((params) => ({
      url: `me/messages/${params.mailId}/attachments/${params.id}/$value`,
      id: params.contentId,
      method: 'GET',
    })), cancellationRef, undefined, (response) => ({ id: response.id, response: response.body }),
  );
}

export type MailApiAccessorReturn = ReturnType<typeof useMailApiAccessor>;

const useMailApiAccessor = (
  tokenProvider: WeakTokenProvider,
): UseMailApiReturnType => {

  /**
   * IDで指定したユーザーの情報を取得
   * IDが指定されていない場合はアプリユーザーの情報を取得
   */
  const fetchUser: FetchUser = React.useCallback(
    (oid) => fetchUserImpl(tokenProvider, oid),
    [tokenProvider],
  );

  /**
   * Outlookメール詳細情報の取得
   */
  const fetchMailDetail: FetchMailDetail = React.useCallback(
    (mailId) => fetchMailDetailImpl(tokenProvider, mailId),
    [tokenProvider],
  );

  /**
   * Outlookメール添付ファイルの取得
   */
  const fetchMailDetailAttachments:FetchMailDetailAttachments = React.useCallback(
    (mailId, attachmentId) => fetchMailDetailAttachmentsImpl(tokenProvider, mailId, attachmentId),
    [tokenProvider],
  );

  /**
   * Outlookメール添付ファイルデータの取得
   */
  const fetchMailAttachment: FetchMailAttachment = React.useCallback(
    (params) => fetchMailAttachmentImpl(tokenProvider, params),
    [tokenProvider],
  );

  /**
   * IDで指定したメールの一覧をバッチリクエストで取得します。
   */

  /**
   * IDで指定した添付ファイルデータのリストをバッチリクエストで取得します。
   */
  const fetchMailAttachments: FetchMailAttachments = React.useCallback(
    (paramsList) => fetchBulkAttachments(
      tokenProvider, paramsList, { current: false },
    ).then((result) => result.responses),
    [tokenProvider],
  );

  return {
    fetchUser: tokenProvider ? fetchUser : undefined,
    fetchMailDetail: tokenProvider ? fetchMailDetail : undefined,
    fetchMailDetailAttachments: tokenProvider ? fetchMailDetailAttachments : undefined,
    fetchMailAttachment: tokenProvider ? fetchMailAttachment : undefined,
    fetchMailAttachments: tokenProvider ? fetchMailAttachments : undefined,
  };
};

export default useMailApiAccessor;
