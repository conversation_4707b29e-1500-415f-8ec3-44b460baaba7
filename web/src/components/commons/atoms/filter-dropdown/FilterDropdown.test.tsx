import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { AsyncState } from 'react-use/lib/useAsync';
import FilterDropdown from './FilterDropdown';
import { SplitViewListMessage, SplitViewListView } from '../../../domains/split-view/split-view-list/SplitViewList';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../../domains/split-view/split-view-detail/SplitViewDetail';
import { ListMode } from '../../../domains/split-view/types/ListMode';
import { SearchListMode } from '../../../domains/split-view/types/SearchListMode';
import { FilterLeftOptionsType } from '../../../../utilities/filter/filterSettings';
import { ISplitViewState } from '../../../domains/split-view/split-view-container/reducers/SplitViewReducer';

jest.mock('../../../../utilities/environment');

describe('FilterDropdown', () => {
  const dispatch = jest.fn();
  const reportEventMock = jest.fn();

  // テスト用の必須 prop: searchFilterReturn
  const searchFilterReturn: [string, (filter: string) => void] = ['', jest.fn()];

  // resultEmptyList を渡したい場合は以下のように定義できます
  const resultEmptyList: AsyncState<FilterLeftOptionsType> = {
    loading: false,
    value: { displayDate: [0, 1, 2, 3, 4, 5, 6], kind: [] },
    error: undefined,
  };

  const state = {
    listView: SplitViewListView.LOADING,
    listMessage: SplitViewListMessage.BLANK,
    list: [],
    activeId: '',
    detailView: SplitViewDetailView.LOADING,
    detailMessage: SplitViewDetailMessage.BLANK,
    detail: undefined,
    context: {
      sort: [],
      filter: [],
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  };
  const listMode = ListMode.SEARCH;

  beforeEach(() => {
    dispatch.mockReset();
    reportEventMock.mockReset();
    (searchFilterReturn[1] as jest.Mock).mockReset();
  });

  describe('className', () => {
    const rootClassName = 'filter-dropdown';

    describe('when className = undefined', () => {
      it('should have only "filter-dropdown"', () => {
        const { container } = render(
          <FilterDropdown
            selectedKey="displayDate"
            selectedOption={0}
            className={undefined}
            state={state}
            listMode={listMode}
            dispatch={dispatch}
            reportEvent={reportEventMock}
            resultEmptyList={resultEmptyList}
            dateFilterRef={{ current: { from: '' } }}
          />,
        );
        expect(container.firstChild).toHaveClass(rootClassName);
        expect(container.firstChild).toHaveClass('filter-dropdown');
        expect(container.firstChild).not.toHaveClass('abc');
      });
    });

    describe('when className = abc', () => {
      it('should have "filter-dropdown" & "abc"', () => {
        const { container } = render(
          <FilterDropdown
            selectedKey="displayDate"
            selectedOption={0}
            className="abc"
            state={state}
            listMode={listMode}
            dispatch={dispatch}
            reportEvent={reportEventMock}
            resultEmptyList={resultEmptyList}
            dateFilterRef={{ current: { from: '' } }}
          />,
        );
        expect(container.firstChild).toHaveClass(rootClassName);
        expect(container.firstChild).toHaveClass('abc');
      });
    });
  });

  describe('placeholder and default selected', () => {
    it('should render placeholder text', () => {
      const { getByText } = render(
        <FilterDropdown
          selectedKey="displayDate"
          selectedOption={undefined}
          className=""
          state={state}
          listMode={listMode}
          dispatch={dispatch}
          reportEvent={reportEventMock}
          resultEmptyList={resultEmptyList}
          dateFilterRef={{ current: { from: '' } }}
        />,
      );
      expect(getByText('期間を選択')).toBeInTheDocument();
    });

    it('should show selected header when selectedOption=0', () => {
      const { getByText } = render(
        <FilterDropdown
          selectedKey="displayDate"
          selectedOption={0}
          className=""
          state={state}
          listMode={listMode}
          dispatch={dispatch}
          reportEvent={reportEventMock}
          resultEmptyList={resultEmptyList}
          dateFilterRef={{ current: { from: '' } }}
        />,
      );
      // デフォルト選択は「期間指定なし」
      expect(getByText('期間指定なし')).toBeInTheDocument();
    });
  });

  describe('AI機能テスト', () => {
    let dateFilterRef: React.MutableRefObject<{ from?: string; to?: string }>;

    beforeEach(() => {
      // dateFilterRefを初期化
      dateFilterRef = { current: { from: '2023-01-01', to: '2023-12-31' } };
      // DOMの設定
      Object.defineProperty(document, 'activeElement', {
        value: null,
        writable: true,
      });
    });
    describe('searchModeによってcount表示が制御される', () => {
      const stateWithList: ISplitViewState = {
        ...state,
        list: [{
          id: 'test-id',
          kind: 'SPO' as const,
          title: 'Test Title',
          note: 'Test Note',
          displayDate: '2023-01-01',
          properties: {},
          reposUpdatedDate: new Date().toISOString(),
        }],
      };
      it('Chatモードでcount=0の場合、result-emptyクラスが適用されない', () => {
        const { container } = render(
          <FilterDropdown
            selectedKey="displayDate"
            selectedOption={1}
            state={stateWithList}
            listMode={listMode}
            dispatch={dispatch}
            reportEvent={reportEventMock}
            resultEmptyList={{
              loading: false,
              value: { displayDate: [0, 0, 0, 0, 0, 0, 0], kind: [] },
              error: undefined,
            }}
            dateFilterRef={dateFilterRef}
            searchMode={SearchListMode.Chat}
          />,
        );

        // チャットモードでは count=0 でも result-empty クラスが付かない
        const item = container.querySelector('.filter-item.result-empty');
        expect(item).not.toBeInTheDocument();
      });
    });
  });
});
