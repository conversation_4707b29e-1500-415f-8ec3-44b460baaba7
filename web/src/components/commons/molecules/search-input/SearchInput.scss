@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.search-input {
  display: block;
  width: 100%;

  @include remove-type-search-appearance;

  input::placeholder {
    color: var(--color-guide-foreground-2);
  }

  input {
    margin: 5px 0px 0px 0px;

    @include media-pc {
      background-color: var(--color-guide-background-2);
    }
  }

  input:hover {
    @include media-pc {
      background-color: var(--color-guide-background-active);
    }
  }

  input:focus {
    border-color: var(--color-guide-brand-main-foreground);

    &::placeholder {
      color: var(--color-placeholder-text);
    }
  }

  &.is-guid-search {
    label {
      // avoid user selection and cursor hover effect
      pointer-events: none;
      display: block;
      overflow: hidden;
      height: 22px;

      // align position
      top: 22px;
      left: -5px;

      // make it do not cover the clear button
      right: 33px;
    }

    input {
      user-select: none;
      pointer-events: none;
      color: transparent;
      height: 32px;
    }
  }

  .search-input-bubble {
    border-color: var(--color-guide-deafult-border);
    background-color: var(--color-guide-background-2);
    height: 20px;
    margin: 0;
    padding-top: 1px;

    // make it can truncate text
    min-width: 0;
    max-width: 100%;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    .ui-pillcontent {
      font-size: 12px;
    }
  }
}

.search-input-find-icon {
  // プレースホルダテキストと色を揃える
  color: var(--color-placeholder-text);
}

.chat-container {
  background: var(--color-guide-foreground-6);
  padding: -10px;
  /* チャット欄の最大高さ */
  max-height: 150px;
  overflow-y: auto;
  border-radius: 6px;

  @include media-sp {
    max-height: 110px;
  }
}

.chat-message {
  // margin-right: -10.4px;
  margin: 5px 0px;
  // border: 0px;

  .ms-FocusZone.ui-chat__message {
    // background: var(--color-guide-foreground-6);
    margin-right: 0px;
    padding: 6px 5px;
    max-width: 100%;
    /* ここで幅を広げる */
    // word-break: keep-all;
    // /* 単語をできる限り折り返さない */
  }
}

/* chat-my-message の見た目を chat-message に合わせる */
/* chat-my-message コンテナをフレックスにして右寄せ */
.chat-my-message {
  display: flex;
  justify-content: flex-end;


  .ms-FocusZone.ui-chat__message {
    width: fit-content;
    /* 必要に応じて max-width も指定できます */
    margin-left: auto;
  }
}