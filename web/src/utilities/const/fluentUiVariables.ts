export const SkeletonWaveColors = {
  animationBackground: 'var(--color-theme-deep1)',
  animationBackgroundSecondary: 'var(--color-theme-deep2)',
};

export const CircleFillButton = {
  color: 'var(--color-text-body)',
  colorDisabled: 'var(--color-theme-light1)',
  borderColor: 'var(--color-theme-light1)',
  borderColorActive: 'var(--color-theme-light1)',
  borderColorDisabled: 'var(--color-theme-middle1-disabled)',
  backgroundColor: 'var(--color-theme-light1)',
  backgroundColorDisabled: 'var(--color-theme-middle1-disabled)',
  backgroundColorActive: 'var(--color-theme-light1-hover)',
  backgroundColorHover: 'var(--color-theme-light1-hover)',
  borderColorHover: 'var(--color-theme-light1-hover)',
};

export const FillDropdown = {
  ...CircleFillButton,
  listBackgroundColor: 'var(--color-theme-light1)',
  listItemContentColor: 'var(--color-text-body)',
  selectedItemBackgroundColor: 'var(--color-theme-light1-hover)',
  listItemBackgroundColorHover: 'var(--color-theme-light1-hover)',
  selectedItemBackgroundColorHover: 'var(--color-theme-light1-hover)',
};
