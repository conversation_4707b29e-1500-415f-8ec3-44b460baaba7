import React from 'react';

/**
 * ReactのDOMイベント
 */
export type ReactEventType =
  React.KeyboardEvent<HTMLElement>
  | React.MouseEvent<HTMLElement>
  | React.FocusEvent<HTMLElement>
  | React.FormEvent<HTMLElement>;

/**
 * Reactのタッチイベント
 */
export type TouchOrMouseEventType = React.SyntheticEvent<HTMLElement, TouchEvent | MouseEvent>;

/**
 * イベントがMouseEventであることをtypeから型判定する
 * @param e 種類を判定していないReactEvent
 * @param [type] デフォルト値は 'click'
 * @returns e.typeがtypeと一致していればtrue
 */
export function isMouseEvent(e: ReactEventType, type = 'click'): e is React.MouseEvent<HTMLElement> {
  return e.type === type;
}

/**
 * イベントがKeyboardEventであることをtypeから型判定する
 * @param e 種類を判定していないReactEventType
 * @param [type] デフォルト値は 'keydown'
 * @returns e.typeがtypeと一致していればtrue
 */
export function isKeyboardEvent(e: ReactEventType, type = 'keydown'): e is React.KeyboardEvent<HTMLElement> {
  return e.type === type;
}

/**
 * イベントがKeyboardEventであるときにキーがEnterであることを判定する
 * さらに、日本語入力中のenterでは発火させない
 * @param e 種類を判定していないReactEventType
 * @param [type] デフォルト値は 'keydown'
 * @returns e.keyが'Enter'のときにtrue。それ以外はfalse
 */
export function isEnterKey(e: ReactEventType, type = 'keydown'): boolean {
  if (!isKeyboardEvent(e, type)) return false;
  if (e.key !== 'Enter') return false;
  // 日本語入力中は発火させない
  return !e.nativeEvent.isComposing;
}

/**
 * イベントがEnterキー押し下げかMouseEventであるかを判定する
 * @param e 種類を判定していないReactEventType
 * @returns eがクリックイベントまたはEnterのkeydownイベントのときにtrue
 */
export function isEnterKeydownOrClick(e: ReactEventType): boolean {
  return isMouseEvent(e) || isEnterKey(e);
}
